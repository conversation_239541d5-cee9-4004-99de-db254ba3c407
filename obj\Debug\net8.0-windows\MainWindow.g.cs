﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C5F01CE2CB8A732040628D2C11FAA1DC9AF223B4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LiveCharts.Wpf;
using PEMTestSystem;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PEMTestSystem {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 384 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VoltageValueText;
        
        #line default
        #line hidden
        
        
        #line 388 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentValueText;
        
        #line default
        #line hidden
        
        
        #line 392 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TemperatureValueText;
        
        #line default
        #line hidden
        
        
        #line 396 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FlowRate1ValueText;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FlowRate2ValueText;
        
        #line default
        #line hidden
        
        
        #line 442 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ChartTitleText;
        
        #line default
        #line hidden
        
        
        #line 453 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart RealTimeChart;
        
        #line default
        #line hidden
        
        
        #line 480 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartButton;
        
        #line default
        #line hidden
        
        
        #line 483 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopButton;
        
        #line default
        #line hidden
        
        
        #line 601 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PowerDeviceRow;
        
        #line default
        #line hidden
        
        
        #line 611 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse PowerStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 620 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PowerSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 649 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TemperatureControllerRow;
        
        #line default
        #line hidden
        
        
        #line 659 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse TemperatureStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 668 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TemperatureDisableButton;
        
        #line default
        #line hidden
        
        
        #line 673 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TemperatureSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 710 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FlowPump1Row;
        
        #line default
        #line hidden
        
        
        #line 720 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse FlowPump1StatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 729 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FlowPump1DisableButton;
        
        #line default
        #line hidden
        
        
        #line 734 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FlowPump1SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 771 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FlowPump2Row;
        
        #line default
        #line hidden
        
        
        #line 781 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse FlowPump2StatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 790 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FlowPump2DisableButton;
        
        #line default
        #line hidden
        
        
        #line 795 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FlowPump2SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 832 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border DatabaseRow;
        
        #line default
        #line hidden
        
        
        #line 842 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse DatabaseStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 851 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DatabaseSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 899 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl ExperimentModeTabControl;
        
        #line default
        #line hidden
        
        
        #line 1060 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ScanByRateRadio;
        
        #line default
        #line hidden
        
        
        #line 1061 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ScanByTimeRadio;
        
        #line default
        #line hidden
        
        
        #line 1064 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ScanRateGroup;
        
        #line default
        #line hidden
        
        
        #line 1072 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ScanTimeGroup;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PEMTestSystem;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.VoltageValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CurrentValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TemperatureValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.FlowRate1ValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.FlowRate2ValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ChartTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            
            #line 446 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportChart_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 448 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowHistory_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.RealTimeChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 10:
            this.StartButton = ((System.Windows.Controls.Button)(target));
            
            #line 481 "..\..\..\MainWindow.xaml"
            this.StartButton.Click += new System.Windows.RoutedEventHandler(this.StartButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.StopButton = ((System.Windows.Controls.Button)(target));
            
            #line 484 "..\..\..\MainWindow.xaml"
            this.StopButton.Click += new System.Windows.RoutedEventHandler(this.StopButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 499 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeviceSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.PowerDeviceRow = ((System.Windows.Controls.Border)(target));
            return;
            case 15:
            this.PowerStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 16:
            this.PowerSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 622 "..\..\..\MainWindow.xaml"
            this.PowerSettingsButton.Click += new System.Windows.RoutedEventHandler(this.PowerSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.TemperatureControllerRow = ((System.Windows.Controls.Border)(target));
            return;
            case 18:
            this.TemperatureStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 19:
            this.TemperatureDisableButton = ((System.Windows.Controls.Button)(target));
            
            #line 670 "..\..\..\MainWindow.xaml"
            this.TemperatureDisableButton.Click += new System.Windows.RoutedEventHandler(this.TemperatureDisableButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.TemperatureSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 675 "..\..\..\MainWindow.xaml"
            this.TemperatureSettingsButton.Click += new System.Windows.RoutedEventHandler(this.TemperatureSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.FlowPump1Row = ((System.Windows.Controls.Border)(target));
            return;
            case 22:
            this.FlowPump1StatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 23:
            this.FlowPump1DisableButton = ((System.Windows.Controls.Button)(target));
            
            #line 731 "..\..\..\MainWindow.xaml"
            this.FlowPump1DisableButton.Click += new System.Windows.RoutedEventHandler(this.FlowPump1DisableButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.FlowPump1SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 736 "..\..\..\MainWindow.xaml"
            this.FlowPump1SettingsButton.Click += new System.Windows.RoutedEventHandler(this.FlowPump1SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.FlowPump2Row = ((System.Windows.Controls.Border)(target));
            return;
            case 26:
            this.FlowPump2StatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 27:
            this.FlowPump2DisableButton = ((System.Windows.Controls.Button)(target));
            
            #line 792 "..\..\..\MainWindow.xaml"
            this.FlowPump2DisableButton.Click += new System.Windows.RoutedEventHandler(this.FlowPump2DisableButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.FlowPump2SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 797 "..\..\..\MainWindow.xaml"
            this.FlowPump2SettingsButton.Click += new System.Windows.RoutedEventHandler(this.FlowPump2SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.DatabaseRow = ((System.Windows.Controls.Border)(target));
            return;
            case 30:
            this.DatabaseStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 31:
            this.DatabaseSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 853 "..\..\..\MainWindow.xaml"
            this.DatabaseSettingsButton.Click += new System.Windows.RoutedEventHandler(this.DatabaseSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.ExperimentModeTabControl = ((System.Windows.Controls.TabControl)(target));
            
            #line 902 "..\..\..\MainWindow.xaml"
            this.ExperimentModeTabControl.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ExperimentModeTabControl_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 33:
            this.ScanByRateRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 1060 "..\..\..\MainWindow.xaml"
            this.ScanByRateRadio.Checked += new System.Windows.RoutedEventHandler(this.ScanMethod_Checked);
            
            #line default
            #line hidden
            return;
            case 34:
            this.ScanByTimeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 1061 "..\..\..\MainWindow.xaml"
            this.ScanByTimeRadio.Checked += new System.Windows.RoutedEventHandler(this.ScanMethod_Checked);
            
            #line default
            #line hidden
            return;
            case 35:
            this.ScanRateGroup = ((System.Windows.Controls.Grid)(target));
            return;
            case 36:
            this.ScanTimeGroup = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 547 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ToggleDeviceState_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

