# 宇电温控器动态温度值处理功能

## 概述

本文档描述了宇电MK008温控器驱动中新实现的动态温度值处理功能。该功能能够根据LED显示仪表状态寄存器（0002H---40003）中的小数位数设置，动态调整温度值的精度显示和处理。

## 功能特性

### 1. 动态小数位数检测

- **自动检测**: 通过读取LED状态寄存器的BIT8和BIT9，自动检测当前设备的小数位数设置
- **支持范围**: 0-3位小数点（00=无小数点, 01=1位, 10=2位, 11=3位）
- **实时更新**: 每次读取温度或状态时都会更新小数位数信息

### 2. 动态温度值转换

- **精确转换**: 根据检测到的小数位数，使用相应的除数/乘数进行温度值转换
- **向后兼容**: 默认使用1位小数，与原有代码保持兼容
- **溢出处理**: 保持原有的LLLL/HHHH溢出检测机制

### 3. LED状态解析

- **完整解析**: 解析LED状态寄存器的所有位信息
- **状态监控**: 监控输出状态、报警状态、温度单位等
- **调试支持**: 提供详细的状态信息用于调试和监控

## 技术实现

### LED状态寄存器位定义

```csharp
private const ushort LED_OUT1_MASK = 0x0001;    // BIT0: OUT1(主控输出1)状态
private const ushort LED_OUT2_MASK = 0x0002;    // BIT1: OUT2(输出2)状态  
private const ushort LED_AT_MASK = 0x0004;      // BIT2: AT(自整定)状态
private const ushort LED_AL1_MASK = 0x0008;     // BIT3: AL1(报警1)状态
private const ushort LED_AL2_MASK = 0x0010;     // BIT4: AL2(报警2)状态
private const ushort LED_ONOFF_MASK = 0x0020;   // BIT5: ON/OFF状态
private const ushort LED_UNIT_MASK = 0x0040;    // BIT6: 温度单位(0=摄氏度, 1=华氏度)
private const ushort LED_LBAT_MASK = 0x0080;    // BIT7: 加热环路LBAT报警
private const ushort LED_DECIMAL_MASK = 0x0300; // BIT8-BIT9: 小数点位数
```

### 小数位数解析算法

```csharp
private void ParseLedStatus(ushort ledStatus)
{
    // 提取小数位数 (BIT8-BIT9)
    var decimalBits = (ledStatus & LED_DECIMAL_MASK) >> 8;
    var newDecimalPlaces = decimalBits switch
    {
        0b00 => 0,  // 无小数点
        0b01 => 1,  // 1位小数
        0b10 => 2,  // 2位小数
        0b11 => 3,  // 3位小数
        _ => 1      // 默认1位小数
    };
    
    _currentDecimalPlaces = newDecimalPlaces;
}
```

### 动态温度转换

```csharp
private double ConvertToTemperature(ushort registerValue)
{
    short signedValue = (short)registerValue;
    
    // 检查溢出标志
    if (signedValue == -16666) return double.NegativeInfinity;
    if (signedValue == 18888) return double.PositiveInfinity;
    
    // 根据当前小数位数计算除数
    var divisor = Math.Pow(10, _currentDecimalPlaces);
    return signedValue / divisor;
}

private ushort ConvertFromTemperature(double temperature)
{
    // 根据当前小数位数计算乘数
    var multiplier = Math.Pow(10, _currentDecimalPlaces);
    var value = (short)(temperature * multiplier);
    return (ushort)value;
}
```

## 新增API

### 属性

- `int CurrentDecimalPlaces`: 获取当前温度显示的小数位数

### 方法

- `Task<Dictionary<string, object>> GetLedStatusAsync()`: 获取LED状态的详细信息

## 使用示例

### 基本使用

```csharp
var controller = new YudianTemperatureControllerDriver("COM3", 9600, 3, "TC001", "温控器");

// 连接设备
await controller.ConnectAsync();

// 读取当前温度（自动使用正确的小数位数）
var currentTemp = await controller.GetCurrentTemperatureAsync();
Console.WriteLine($"当前温度: {currentTemp.ToString($"F{controller.CurrentDecimalPlaces}")}°C");

// 设置目标温度（自动使用正确的小数位数）
await controller.SetTargetTemperatureAsync(25.5);

// 获取LED状态详情
var ledStatus = await controller.GetLedStatusAsync();
Console.WriteLine($"小数位数: {ledStatus["DecimalPlaces"]}");
Console.WriteLine($"温度单位: {ledStatus["TemperatureUnit"]}");
Console.WriteLine($"加热状态: {ledStatus["IsHeating"]}");
```

### 高级监控

```csharp
// 获取完整的设备信息（包含动态精度信息）
var deviceInfo = await controller.GetDeviceInfoAsync();
var currentDecimalPlaces = (int)deviceInfo.Properties["CurrentDecimalPlaces"];
var temperatureUnit = (string)deviceInfo.Properties["TemperatureUnit"];
var ledStatusRaw = (ushort)deviceInfo.Properties["LEDStatus"];

Console.WriteLine($"设备当前配置:");
Console.WriteLine($"  小数位数: {currentDecimalPlaces}");
Console.WriteLine($"  温度单位: {temperatureUnit}");
Console.WriteLine($"  LED状态: 0x{ledStatusRaw:X4}");
```

## 兼容性说明

### 向后兼容

- 现有代码无需修改即可使用新功能
- 默认小数位数为1位，与原有行为一致
- 所有原有API保持不变

### 性能影响

- 每次温度读取时会额外读取LED状态寄存器
- 增加的通信开销很小（通常一次读取多个连续寄存器）
- LED状态解析计算量很小，对性能影响可忽略

## 错误处理

### 异常情况处理

1. **LED状态读取失败**: 保持当前小数位数设置不变
2. **小数位数解析异常**: 使用默认1位小数
3. **温度转换溢出**: 返回正/负无穷大

### 日志记录

- 小数位数变化时记录Debug级别日志
- 温度转换时记录详细的Debug信息
- LED状态解析异常时记录Warning级别日志

## 测试验证

### 单元测试

运行测试程序验证功能：

```bash
# 编译并运行测试
dotnet run --project Tests\YudianTemperatureControllerDynamicPrecisionTest.cs
```

### 硬件测试

1. 连接宇电MK008温控器
2. 修改设备的小数位数设置
3. 验证驱动能够正确检测并使用新的小数位数
4. 检查温度值显示精度是否正确

## 注意事项

1. **设备配置**: 确保温控器的小数位数设置与实际需求匹配
2. **通信稳定性**: LED状态读取失败时会保持原有设置，不会影响基本功能
3. **精度限制**: 小数位数仅影响显示精度，不改变设备内部的实际精度
4. **单位转换**: 目前仅支持摄氏度，华氏度转换需要额外实现

## 更新日志

- **v1.1.0**: 新增动态小数位数检测和处理功能
- **v1.1.0**: 新增LED状态完整解析功能
- **v1.1.0**: 新增GetLedStatusAsync方法
- **v1.1.0**: 更新所有温度相关方法支持动态精度
