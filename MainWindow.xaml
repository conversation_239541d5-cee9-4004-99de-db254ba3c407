<Window x:Class="PEMTestSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:PEMTestSystem"
        xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
        xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        mc:Ignorable="d"
        Title="PEM 电解槽自动化测试系统" Height="800" Width="1400"
        WindowState="Maximized" 
        Background="#1a1c23"
        Foreground="#c0c2c9"
        FontFamily="Roboto, Microsoft YaHei"
        FontSize="14">
    <Window.Resources>
        <!-- 颜色定义 -->
        <SolidColorBrush x:Key="BgColor" Color="#1a1c23"/>
        <SolidColorBrush x:Key="PanelColor" Color="#242731"/>
        <SolidColorBrush x:Key="BorderColor" Color="#3a3f51"/>
        <SolidColorBrush x:Key="PrimaryColor" Color="#00aaff"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#e1e1e1"/>
        <SolidColorBrush x:Key="TextColor" Color="#c0c2c9"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#00ff9b"/>
        <SolidColorBrush x:Key="DangerColor" Color="#ff4d4d"/>
        <SolidColorBrush x:Key="DisabledBgColor" Color="#444444"/>
        <SolidColorBrush x:Key="DisabledFgColor" Color="#888888"/>

        <!-- 自定义滚动条样式 -->
        <Style x:Key="CustomScrollBarThumb" TargetType="{x:Type Thumb}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Thumb}">
                        <Border x:Name="ThumbBorder" 
                                Background="{StaticResource PrimaryColor}" 
                                CornerRadius="3" 
                                Opacity="0.7"/>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ThumbBorder" Property="Opacity" Value="1"/>
                            </Trigger>
                            <Trigger Property="IsDragging" Value="True">
                                <Setter TargetName="ThumbBorder" Property="Background" Value="{StaticResource SuccessColor}"/>
                                <Setter TargetName="ThumbBorder" Property="Opacity" Value="1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CustomScrollBar" TargetType="{x:Type ScrollBar}">
            <Setter Property="Background" Value="{StaticResource BorderColor}"/>
            <Setter Property="Width" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollBar}">
                        <Border Background="{TemplateBinding Background}" CornerRadius="6">
                            <Track x:Name="PART_Track" IsDirectionReversed="True" Margin="2">
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource CustomScrollBarThumb}"/>
                                </Track.Thumb>
                                <Track.IncreaseRepeatButton>
                                    <RepeatButton Command="ScrollBar.PageDownCommand" Opacity="0" Focusable="False"/>
                                </Track.IncreaseRepeatButton>
                                <Track.DecreaseRepeatButton>
                                    <RepeatButton Command="ScrollBar.PageUpCommand" Opacity="0" Focusable="False"/>
                                </Track.DecreaseRepeatButton>
                            </Track>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="Orientation" Value="Horizontal">
                    <Setter Property="Width" Value="Auto"/>
                    <Setter Property="Height" Value="12"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="{x:Type ScrollBar}">
                                <Border Background="{TemplateBinding Background}" CornerRadius="6">
                                    <Track x:Name="PART_Track" IsDirectionReversed="False" Margin="2">
                                        <Track.Thumb>
                                            <Thumb Style="{StaticResource CustomScrollBarThumb}"/>
                                        </Track.Thumb>
                                        <Track.IncreaseRepeatButton>
                                            <RepeatButton Command="ScrollBar.PageRightCommand" Opacity="0" Focusable="False"/>
                                        </Track.IncreaseRepeatButton>
                                        <Track.DecreaseRepeatButton>
                                            <RepeatButton Command="ScrollBar.PageLeftCommand" Opacity="0" Focusable="False"/>
                                        </Track.DecreaseRepeatButton>
                                    </Track>
                                </Border>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CustomScrollViewer" TargetType="{x:Type ScrollViewer}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollViewer}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <ScrollContentPresenter Grid.Column="0" Grid.Row="0" 
                                                  Content="{TemplateBinding Content}" 
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"/>
                            <ScrollBar Grid.Column="1" Grid.Row="0" 
                                     x:Name="PART_VerticalScrollBar"
                                     Style="{StaticResource CustomScrollBar}"
                                     Orientation="Vertical"
                                     Value="{TemplateBinding VerticalOffset}"
                                     Maximum="{TemplateBinding ScrollableHeight}"
                                     ViewportSize="{TemplateBinding ViewportHeight}"
                                     Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"/>
                            <ScrollBar Grid.Column="0" Grid.Row="1" 
                                     x:Name="PART_HorizontalScrollBar"
                                     Style="{StaticResource CustomScrollBar}"
                                     Orientation="Horizontal"
                                     Value="{TemplateBinding HorizontalOffset}"
                                     Maximum="{TemplateBinding ScrollableWidth}"
                                     ViewportSize="{TemplateBinding ViewportWidth}"
                                     Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 转换器 -->
        <local:BoolToStrikethroughConverter x:Key="BoolToStrikethroughConverter"/>
        <local:BoolToButtonContentConverter x:Key="BoolToButtonContentConverter"/>
        <local:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

        <!-- 基础按钮样式 -->
        <Style TargetType="Button" x:Key="BaseButton">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" CornerRadius="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource DisabledBgColor}"/>
                                <Setter Property="Foreground" Value="{StaticResource DisabledFgColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 特定按钮样式 -->
        <Style TargetType="Button" x:Key="StartButton" BasedOn="{StaticResource BaseButton}">
            <Setter Property="Background" Value="{StaticResource SuccessColor}"/>
            <Setter Property="Foreground" Value="#000"/>
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="Height" Value="50"/>
        </Style>
        <Style TargetType="Button" x:Key="StopButton" BasedOn="{StaticResource BaseButton}">
            <Setter Property="Background" Value="{StaticResource DangerColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="Height" Value="50"/>
        </Style>
        <Style TargetType="Button" x:Key="SecondaryButton">
            <Setter Property="Background" Value="{StaticResource BgColor}"/>
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,3"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" CornerRadius="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource SecondaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 卡片样式 -->
        <Style TargetType="Border" x:Key="CardStyle">
            <Setter Property="Background" Value="{StaticResource PanelColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
        </Style>
        <Style TargetType="TextBlock" x:Key="CardTitleStyle">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryColor}"/>
        </Style>
        <Style TargetType="TextBox">
            <Setter Property="Background" Value="{StaticResource BgColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryColor}"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CaretBrush" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="border" CornerRadius="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsKeyboardFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- TabControl 样式 -->
        <Style TargetType="TabControl" x:Key="MainTabControlStyle">
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabControl">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <TabPanel Grid.Row="0" IsItemsHost="True" Background="Transparent" Margin="0,0,0,-1"/>
                            <Border Grid.Row="1" BorderBrush="{StaticResource BorderColor}" BorderThickness="0,1,0,0">
                                <ContentPresenter ContentSource="SelectedContent"/>
                            </Border>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style TargetType="TabItem" x:Key="MainTabItemStyle">
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border x:Name="border" BorderBrush="Transparent" BorderThickness="0,0,0,2" Margin="0,0,10,0">
                            <ContentPresenter ContentSource="Header"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Border Grid.Row="0" Background="{StaticResource PanelColor}" BorderBrush="{StaticResource BorderColor}" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal" Margin="20,10">
                <Path Data="M50,38 A12,12 0 1 1 50,62 A12,12 0 1 1 50,38 Z M50,5 A45,18 0 0 1 50,95 A45,18 0 0 1 50,5 Z" Stroke="{StaticResource PrimaryColor}" StrokeThickness="5" Fill="{StaticResource PrimaryColor}" Width="32" Height="32" Stretch="Uniform" RenderTransformOrigin="0.5,0.5">
                    <Path.RenderTransform>
                        <TransformGroup>
                            <RotateTransform Angle="30"/>
                        </TransformGroup>
                    </Path.RenderTransform>
                </Path>
                <Path Data="M50,5 A45,18 0 0 1 50,95 A45,18 0 0 1 50,5 Z" Stroke="{StaticResource PrimaryColor}" StrokeThickness="5" Fill="Transparent" Width="32" Height="32" Stretch="Uniform" RenderTransformOrigin="0.5,0.5" Margin="-32,0,0,0">
                    <Path.RenderTransform>
                        <TransformGroup>
                            <RotateTransform Angle="-30"/>
                        </TransformGroup>
                    </Path.RenderTransform>
                </Path>
                <TextBlock Text="PEM 电解槽自动化测试系统" VerticalAlignment="Center" FontSize="20" FontWeight="SemiBold" Foreground="{StaticResource SecondaryColor}" Margin="15,0,0,0"/>
            </StackPanel>
        </Border>
        <Grid Grid.Row="1" Margin="15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="15"/>
                <ColumnDefinition Width="380"/>
            </Grid.ColumnDefinitions>
            <DockPanel Grid.Column="0" >
                <!-- 实时数据监控面板 - 固定在顶部 -->
                <Border DockPanel.Dock="Top" Style="{StaticResource CardStyle}" Margin="0,0,0,15">
                    <DockPanel>
                        <DockPanel DockPanel.Dock="Top">
                            <TextBlock Text="实时数据监控" Style="{StaticResource CardTitleStyle}" DockPanel.Dock="Left"/>
                        </DockPanel>
                        <Separator DockPanel.Dock="Top" Background="{StaticResource BorderColor}" Margin="0,10,0,15"/>
                        <UniformGrid Columns="7" Rows="1">
                            <StackPanel Margin="0,0,20,10">
                                <TextBlock Text="电解槽电压" Foreground="{StaticResource TextColor}" FontSize="15"/>
                                <TextBlock x:Name="VoltageValueText" Text="-- V" Foreground="{StaticResource SecondaryColor}" FontSize="26" FontWeight="SemiBold"/>
                            </StackPanel>
                            <StackPanel Margin="0,0,20,10">
                                <TextBlock Text="电解槽电流" Foreground="{StaticResource TextColor}" FontSize="15"/>
                                <TextBlock x:Name="CurrentValueText" Text="-- A" Foreground="{StaticResource SecondaryColor}" FontSize="26" FontWeight="SemiBold"/>
                            </StackPanel>
                            <StackPanel Margin="0,0,20,10">
                                <TextBlock Text="当前温度" Foreground="{StaticResource TextColor}" FontSize="15"/>
                                <TextBlock x:Name="TemperatureValueText" Text="-- °C" Foreground="{StaticResource SecondaryColor}" FontSize="26" FontWeight="SemiBold"/>
                            </StackPanel>
                            <StackPanel Margin="0,0,20,10">
                                <TextBlock Text="流量泵 1 流量" Foreground="{StaticResource TextColor}" FontSize="15"/>
                                <TextBlock x:Name="FlowRate1ValueText" Text="-- L/min" Foreground="{StaticResource SecondaryColor}" FontSize="26" FontWeight="SemiBold"/>
                            </StackPanel>
                            <StackPanel Margin="0,0,20,10">
                                <TextBlock Text="流量泵 2 流量" Foreground="{StaticResource TextColor}" FontSize="15"/>
                                <TextBlock x:Name="FlowRate2ValueText" Text="-- L/min" Foreground="{StaticResource SecondaryColor}" FontSize="26" FontWeight="SemiBold"/>
                            </StackPanel>
                        </UniformGrid>
                    </DockPanel>
                </Border>

                <!-- 实验进度面板 - 固定在底部 -->
                <Border DockPanel.Dock="Bottom" Style="{StaticResource CardStyle}">
                    <DockPanel>
                        <TextBlock Text="实验进度" DockPanel.Dock="Top" Style="{StaticResource CardTitleStyle}"/>
                        <Separator DockPanel.Dock="Top" Background="{StaticResource BorderColor}" Margin="0,10,0,15"/>
                        <StackPanel>
                            <UniformGrid Columns="6">
                                <StackPanel Margin="0,10,20,0">
                                    <TextBlock Text="实验开始时刻" FontSize="14" Foreground="{StaticResource TextColor}"/>
                                    <TextBlock Text="05-28 10:30:15" FontSize="18" FontWeight="SemiBold"/>
                                </StackPanel>
                                <StackPanel Margin="0,10,20,0">
                                    <TextBlock Text="已用时间" FontSize="14" Foreground="{StaticResource TextColor}"/>
                                    <TextBlock Text="00:15:32" FontSize="18" FontWeight="SemiBold"/>
                                </StackPanel>
                                <StackPanel Margin="0,10,20,0">
                                    <TextBlock Text="循环进度" FontSize="14" Foreground="{StaticResource TextColor}"/>
                                    <TextBlock Text="1 / 1" FontSize="18" FontWeight="SemiBold"/>
                                </StackPanel>
                                <StackPanel Margin="0,10,20,0">
                                    <TextBlock Text="采样周期" FontSize="14" Foreground="{StaticResource TextColor}"/>
                                    <TextBlock Text="1000 ms" FontSize="18" FontWeight="SemiBold"/>
                                </StackPanel>
                                <StackPanel Margin="0,10,20,0">
                                    <TextBlock Text="数据点" FontSize="14" Foreground="{StaticResource TextColor}"/>
                                    <TextBlock Text="932" FontSize="18" FontWeight="SemiBold"/>
                                </StackPanel>
                            </UniformGrid>
                        </StackPanel>
                    </DockPanel>
                </Border>

                <!-- 实时曲线图面板 - 填充剩余空间 -->
                <Border Style="{StaticResource CardStyle}" Margin="0,0,0,15">
                    <DockPanel>
                        <Grid DockPanel.Dock="Top" Margin="0,0,0,15">
                            <TextBlock x:Name="ChartTitleText" Text="恒流模式: 电压-时间 (V-t) 曲线" 
                                       Style="{StaticResource CardTitleStyle}" VerticalAlignment="Center"/>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <Button Content="导出" Style="{StaticResource SecondaryButton}" Margin="0,0,5,0" 
                                        Click="ExportChart_Click"/>
                                <Button Content="历史记录" Style="{StaticResource SecondaryButton}" 
                                        Click="ShowHistory_Click"/>
                            </StackPanel>
                        </Grid>

                        <!-- LiveCharts 图表区域 -->
                        <lvc:CartesianChart x:Name="RealTimeChart" 
                                            Series="{Binding ChartSeries}"
                                            AxisX="{Binding XAxis}"
                                            AxisY="{Binding YAxis}"
                                            Background="{StaticResource BgColor}"
                                            Foreground="{StaticResource TextColor}"
                                            AnimationsSpeed="0:0:0.5"
                                            Hoverable="False"
                                            DisableAnimations="False">
                            <lvc:CartesianChart.Resources>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </lvc:CartesianChart.Resources>
                        </lvc:CartesianChart>
                    </DockPanel>
                </Border>
            </DockPanel>
            <DockPanel Grid.Column="2">
                <Border DockPanel.Dock="Bottom" Background="{StaticResource PanelColor}" BorderBrush="{StaticResource BorderColor}" BorderThickness="1" CornerRadius="8" Padding="20" Margin="0,15,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Button x:Name="StartButton" Grid.Column="0" Content="开始实验" 
                                Style="{StaticResource StartButton}" Click="StartButton_Click" 
                                Height="50"/>
                        <Button x:Name="StopButton" Grid.Column="2" Content="停止实验" 
                                Style="{StaticResource StopButton}" IsEnabled="False" Click="StopButton_Click"
                                Height="50"/>
                    </Grid>
                </Border>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 设备状态面板 - 固定高度 -->
                    <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,15">
                        <DockPanel>
                            <Grid DockPanel.Dock="Top">
                                <TextBlock Text="设备状态" Style="{StaticResource CardTitleStyle}" VerticalAlignment="Center"/>
                                <Button Content="设置" Style="{StaticResource SecondaryButton}" HorizontalAlignment="Right" VerticalAlignment="Center" ToolTip="打开设备连接设置" Click="DeviceSettingsButton_Click"/>
                            </Grid>
                            <Separator DockPanel.Dock="Top" Background="{StaticResource BorderColor}" Margin="0,10,0,15"/>
                            <ItemsControl ItemsSource="{Binding Devices}" BorderThickness="0" Background="Transparent">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate DataType="{x:Type local:DeviceStatus}">
                                        <Grid Margin="0,0,0,12">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <StackPanel Orientation="Horizontal" Grid.Column="0" VerticalAlignment="Center">
                                                <Ellipse Width="10" Height="10" VerticalAlignment="Center">
                                                    <Ellipse.Style>
                                                        <Style TargetType="Ellipse">
                                                            <Setter Property="Fill" Value="{StaticResource DangerColor}"/>
                                                            <Style.Triggers>
                                                                <MultiDataTrigger>
                                                                    <MultiDataTrigger.Conditions>
                                                                        <Condition Binding="{Binding IsEnabled}" Value="True"/>
                                                                        <Condition Binding="{Binding IsConnected}" Value="True"/>
                                                                    </MultiDataTrigger.Conditions>
                                                                    <Setter Property="Fill" Value="{StaticResource SuccessColor}"/>
                                                                </MultiDataTrigger>
                                                                <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                                                    <Setter Property="Fill" Value="{StaticResource DisabledFgColor}"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Ellipse.Style>
                                                </Ellipse>
                                                <TextBlock Text="{Binding Name}" Margin="10,0,0,0" VerticalAlignment="Center">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Setter Property="TextDecorations" Value="{x:Null}"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                                                    <Setter Property="TextDecorations" Value="Strikethrough"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </StackPanel>

                                            <Button Grid.ColumnSpan="2" HorizontalAlignment="Right" Margin="0,0,70,0"
                                                    Content="{Binding IsEnabled, Converter={StaticResource BoolToButtonContentConverter}}"
                                                    Visibility="{Binding CanBeDisabled, Converter={StaticResource BoolToVisibilityConverter}}"
                                                    Click="ToggleDeviceState_Click">
                                                <!-- 关键修改：将样式合并到一处 -->
                                                <Button.Style>
                                                    <Style TargetType="Button" BasedOn="{StaticResource SecondaryButton}">
                                                        <Setter Property="Opacity" Value="0"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsMouseOver, RelativeSource={RelativeSource AncestorType=ContentPresenter}}" Value="True">
                                                                <Setter Property="Opacity" Value="1"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                            </Button>

                                            <TextBlock Grid.Column="1" HorizontalAlignment="Right" VerticalAlignment="Center">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Setter Property="Text" Value="连接异常"/>
                                                        <Setter Property="Foreground" Value="{StaticResource DangerColor}"/>
                                                        <Style.Triggers>
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}" Value="True"/>
                                                                    <Condition Binding="{Binding IsConnected}" Value="True"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="Text" Value="已连接"/>
                                                                <Setter Property="Foreground" Value="{StaticResource SuccessColor}"/>
                                                            </MultiDataTrigger>
                                                            <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                                                <Setter Property="Text" Value="已禁用"/>
                                                                <Setter Property="Foreground" Value="{StaticResource DisabledFgColor}"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Grid>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </DockPanel>
                    </Border>

                    <!-- 电源控制面板 -->
                    <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,15" VerticalAlignment="Bottom">
                        <DockPanel>
                            <Grid DockPanel.Dock="Top">
                                <TextBlock Text="电源控制" Style="{StaticResource CardTitleStyle}" VerticalAlignment="Center"/>
                            </Grid>
                            <Separator DockPanel.Dock="Top" Background="{StaticResource BorderColor}" Margin="0,10,0,15"/>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 电压设置 -->
                                <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,10">
                                    <TextBlock Text="设定电压 (V)" Foreground="{StaticResource TextColor}" FontSize="14" Margin="0,0,0,5"/>
                                    <TextBox x:Name="PowerVoltageSetBox" Text="1.80" FontSize="14" Padding="8,6" ToolTip="范围：0-150V"/>
                                </StackPanel>

                                <!-- 电流设置 -->
                                <StackPanel Grid.Row="0" Grid.Column="1" Margin="0,0,10,10">
                                    <TextBlock Text="设定电流 (A)" Foreground="{StaticResource TextColor}" FontSize="14" Margin="0,0,0,5"/>
                                    <TextBox x:Name="PowerCurrentSetBox" Text="30.0" FontSize="14" Padding="8,6" ToolTip="范围：0-30A"/>
                                </StackPanel>

                                <!-- 工作模式 -->
                                <StackPanel Grid.Row="0" Grid.Column="2" Margin="0,0,0,10">
                                    <TextBlock Text="工作模式" Foreground="{StaticResource TextColor}" FontSize="14" Margin="0,0,0,5"/>
                                    <ComboBox x:Name="PowerModeCombo" FontSize="14" Padding="8,6">
                                        <ComboBoxItem Content="恒压模式" IsSelected="True"/>
                                        <ComboBoxItem Content="恒流模式"/>
                                    </ComboBox>
                                </StackPanel>

                                <!-- 控制按钮 -->
                                <StackPanel Grid.Row="1" Grid.ColumnSpan="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                                    <Button x:Name="PowerSetButton" Content="设置参数" Style="{StaticResource SecondaryButton}"
                                            Margin="0,0,10,0" Click="PowerSetButton_Click" ToolTip="设置电源输出参数"/>
                                    <Button x:Name="PowerOnButton" Content="开启输出" Style="{StaticResource StartButton}"
                                            Margin="0,0,10,0" Click="PowerOnButton_Click" ToolTip="开启电源输出"/>
                                    <Button x:Name="PowerOffButton" Content="关闭输出" Style="{StaticResource StopButton}"
                                            Click="PowerOffButton_Click" ToolTip="关闭电源输出"/>
                                </StackPanel>

                                <!-- 状态显示 -->
                                <StackPanel Grid.Row="2" Grid.ColumnSpan="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                                    <TextBlock Text="输出状态：" Foreground="{StaticResource TextColor}" FontSize="14" VerticalAlignment="Center"/>
                                    <TextBlock x:Name="PowerOutputStatusText" Text="关闭" Foreground="{StaticResource DangerColor}"
                                               FontSize="14" FontWeight="SemiBold" VerticalAlignment="Center" Margin="5,0,20,0"/>
                                    <TextBlock Text="工作模式：" Foreground="{StaticResource TextColor}" FontSize="14" VerticalAlignment="Center"/>
                                    <TextBlock x:Name="PowerWorkingModeText" Text="恒压" Foreground="{StaticResource PrimaryColor}"
                                               FontSize="14" FontWeight="SemiBold" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                </StackPanel>
                            </Grid>
                        </DockPanel>
                    </Border>

                    <!-- 实验模式与参数设置面板 - 使用剩余空间并支持滚动 -->
                    <Border Grid.Row="1" Style="{StaticResource CardStyle}">
                        <DockPanel>
                            <Grid DockPanel.Dock="Top">
                                <TextBlock Text="实验模式与参数设置" Style="{StaticResource CardTitleStyle}" VerticalAlignment="Center"/>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                    <Button Content="选择" Style="{StaticResource SecondaryButton}" Margin="0,0,5,0"/>
                                    <Button Content="保存" Style="{StaticResource SecondaryButton}"/>
                                </StackPanel>
                            </Grid>
                            <Separator DockPanel.Dock="Top" Background="{StaticResource BorderColor}" Margin="0,10,0,10"/>
                            <TextBlock DockPanel.Dock="Top" Text="配置文件: 新电极-老化测试.json" FontSize="14" Foreground="{StaticResource PrimaryColor}" Margin="0,0,0,15"/>

                            <!-- 关键修改：添加自定义滚动条样式 -->
                            <ScrollViewer VerticalScrollBarVisibility="Auto" 
                                          HorizontalScrollBarVisibility="Disabled"
                                          Style="{StaticResource CustomScrollViewer}">
                                <TabControl x:Name="ExperimentModeTabControl" 
                                            Style="{StaticResource MainTabControlStyle}" 
                                            ItemContainerStyle="{StaticResource MainTabItemStyle}"
                                            SelectionChanged="ExperimentModeTabControl_SelectionChanged">
                                    <TabItem Header="恒流" Tag="ConstantCurrent">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto" 
                                                      Style="{StaticResource CustomScrollViewer}">
                                            <StackPanel Margin="0,15,0,0">
                                                <TextBlock Text="通用参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="目标温度 (°C)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="80" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：室温~90°C"/>
                                                    <TextBlock Text="流量泵1流量 (L/min)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="1.25" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 L/min"/>
                                                    <TextBlock Text="流量泵2流量 (L/min)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="0.00" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 L/min"/>
                                                    <TextBlock Text="重复次数" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="1" Grid.Row="3" Grid.Column="1" Margin="5" ToolTip="范围：1~999"/>
                                                    <TextBlock Text="采样间隔 (秒)" Grid.Row="4" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="1" Grid.Row="4" Grid.Column="1" Margin="5" ToolTip="范围：0.1~60秒"/>
                                                </Grid>
                                                <Separator Background="{StaticResource BorderColor}" Margin="0,15"/>
                                                <TextBlock Text="恒定电流模式特有参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="目标电流 (A)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="20.00" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：0~170A，精度：0.01A"/>
                                                    <TextBlock Text="实验持续时间 (秒)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="3600" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：1~999999秒"/>
                                                    <TextBlock Text="电压上限保护 (V)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="2.500" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，精度：0.001V"/>
                                                    <TextBlock Text="电压下限保护 (V)" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="0.000" Grid.Row="3" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，精度：0.001V"/>
                                                </Grid>
                                            </StackPanel>
                                        </ScrollViewer>
                                    </TabItem>
                                    <TabItem Header="恒压" Tag="ConstantVoltage">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                      Style="{StaticResource CustomScrollViewer}">
                                            <StackPanel Margin="0,15,0,0">
                                                <TextBlock Text="通用参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="目标温度 (°C)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="80" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：室温~90°C"/>
                                                    <TextBlock Text="流量泵1流量 (L/min)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="1.25" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 L/min"/>
                                                    <TextBlock Text="流量泵2流量 (L/min)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="0.00" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 L/min"/>
                                                    <TextBlock Text="重复次数" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="1" Grid.Row="3" Grid.Column="1" Margin="5" ToolTip="范围：1~999"/>
                                                    <TextBlock Text="采样间隔 (秒)" Grid.Row="4" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="1" Grid.Row="4" Grid.Column="1" Margin="5" ToolTip="范围：0.1~60秒"/>
                                                </Grid>
                                                <Separator Background="{StaticResource BorderColor}" Margin="0,15"/>
                                                <TextBlock Text="恒定电压模式特有参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="目标电压 (V)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="1.800" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，精度：0.001V"/>
                                                    <TextBlock Text="实验持续时间 (秒)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="3600" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：1~999999秒"/>
                                                    <TextBlock Text="电流上限保护 (A)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="100.00" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="范围：0~170A，精度：0.01A"/>
                                                    <TextBlock Text="电流下限保护 (A)" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="0.00" Grid.Row="3" Grid.Column="1" Margin="5" ToolTip="范围：0~170A，精度：0.01A"/>
                                                </Grid>
                                            </StackPanel>
                                        </ScrollViewer>
                                    </TabItem>
                                    <TabItem Header="线扫" Tag="LinearScan">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                      Style="{StaticResource CustomScrollViewer}">
                                            <StackPanel Margin="0,15,0,0">
                                                <TextBlock Text="通用参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="目标温度 (°C)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="80" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：室温~90°C"/>
                                                    <TextBlock Text="流量泵1流量 (L/min)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="1.25" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 L/min"/>
                                                    <TextBlock Text="流量泵2流量 (L/min)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="0.00" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 L/min"/>
                                                    <TextBlock Text="重复次数" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="1" Grid.Row="3" Grid.Column="1" Margin="5" ToolTip="范围：1~999"/>
                                                    <TextBlock Text="采样间隔 (秒)" Grid.Row="4" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="1" Grid.Row="4" Grid.Column="1" Margin="5" ToolTip="范围：0.1~60秒"/>
                                                </Grid>
                                                <Separator Background="{StaticResource BorderColor}" Margin="0,15"/>
                                                <TextBlock Text="线性提升电压模式特有参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="起始电压 (V)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="1.200" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，精度：0.001V"/>
                                                    <TextBlock Text="终点电压 (V)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="2.200" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，精度：0.001V"/>
                                                    <TextBlock Text="变化方式" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                                        <RadioButton x:Name="ScanByRateRadio" Content="斜率" IsChecked="True" GroupName="ScanMethod" Checked="ScanMethod_Checked" Foreground="White"/>
                                                        <RadioButton x:Name="ScanByTimeRadio" Content="时间" GroupName="ScanMethod" Margin="10,0,0,0" Checked="ScanMethod_Checked" Foreground="White"/>
                                                    </StackPanel>
                                                </Grid>
                                                <Grid x:Name="ScanRateGroup" Margin="0,5,0,0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Text="变化斜率 (V/s)" VerticalAlignment="Center"/>
                                                    <TextBox Grid.Column="1" Text="0.01" Margin="5" ToolTip="与变化时间二选一"/>
                                                </Grid>
                                                <Grid x:Name="ScanTimeGroup" Visibility="Collapsed" Margin="0,5,0,0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Text="变化时间 (秒)" VerticalAlignment="Center"/>
                                                    <TextBox Grid.Column="1" Text="100" Margin="5" ToolTip="与变化斜率二选一"/>
                                                </Grid>
                                                <Grid Margin="0,10,0,0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="电流上限保护 (A)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="100.00" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：0~170A，精度：0.01A"/>
                                                    <TextBlock Text="电流下限保护 (A)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="0.00" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0~170A，精度：0.01A"/>
                                                    <TextBlock Text="终点保持时间 (秒)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox Text="60" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="到达终点电压后的保持时间"/>
                                                </Grid>
                                            </StackPanel>
                                        </ScrollViewer>
                                    </TabItem>
                                </TabControl>
                            </ScrollViewer>
                        </DockPanel>
                    </Border>
                </Grid>
            </DockPanel>
        </Grid>
    </Grid>
</Window>